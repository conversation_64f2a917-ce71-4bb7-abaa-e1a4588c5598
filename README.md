# EKS Main VPC Terraform Project

This Terraform project creates a VPC with 8 subnets designed for EKS and Transit Gateway integration using a modular structure. The project follows best practices by separating backend infrastructure from main infrastructure to avoid circular dependencies.

## 🚨 Important: New Architecture

This project has been restructured to follow Terraform best practices:

- **Bootstrap Project**: Separate project for S3 backend and DynamoDB table
- **Main Project**: EKS infrastructure that uses the bootstrap-created backend
- **No Circular Dependencies**: Eliminates the dangerous "chicken-and-egg" problem

## Project Structure

```
eks-main/
├── bootstrap/                    # Backend infrastructure (runs once)
│   ├── main.tf                  # S3 bucket and DynamoDB table
│   ├── variables.tf             # Bootstrap variables
│   ├── outputs.tf               # Backend configuration outputs
│   ├── terraform.tfvars         # Bootstrap configuration
│   └── README.md                # Bootstrap documentation
├── config/                      # Backend configuration files
│   ├── dev.backend.hcl          # Development backend config
│   └── prod.backend.hcl         # Production backend config
├── provider.tf                  # AWS provider and backend configuration
├── variables.tf                 # Variable definitions
├── outputs.tf                   # Output definitions
├── vpc.tf                       # VPC, Internet Gateway, Security Groups
├── subnets.tf                   # General use subnets and routing
├── transit_gateway_attachment.tf # TGW attachment subnets and routing
├── dev.tfvars                   # Development environment variables
├── production.tfvars            # Production environment variables
├── SETUP_GUIDE.md               # Complete setup instructions
├── README.md                    # This file
└── .gitignore                   # Git ignore rules
```

## Architecture Overview

- **VPC CIDR**: **********/16
- **Total Subnets**: 8
  - **2 Transit Gateway Attachment Subnets**: For connecting to shared Transit Gateway
  - **2 Private EKS Subnets**: For EKS worker nodes
  - **2 Private Load Balancer Subnets**: For internal load balancers
  - **2 Intra (Isolated) Subnets**: For isolated resources with no internet access
- **Availability Zones**: eu-west-1a and eu-west-1b
- **Naming Convention**: Descriptive names matching AWS best practices

## Resources Created

### Core Networking (vpc.tf)
- 1 VPC (**********/16)
- 1 Security Group for general use

### Application Subnets (subnets.tf)
- **Private EKS Subnets (2)**: For EKS worker nodes in eu-west-1a and eu-west-1b
- **Private Load Balancer Subnets (2)**: For internal load balancers in eu-west-1a and eu-west-1b
- **Intra (Isolated) Subnets (2)**: For isolated resources with no internet access in eu-west-1a and eu-west-1b
- **Individual Route Tables**: Each subnet has its own route table for granular control
- **Proper EKS Tags**: EKS-specific tags for internal load balancer discovery

### Transit Gateway Attachment (transit_gateway_attachment.tf)
- 2 Transit Gateway attachment subnets with specific names:
  - TGW-ECOMM-DEV-1a (eu-west-1a)
  - TGW-ECOMM-DEV-1b (eu-west-1b)
- 2 Individual Route Tables (one per subnet with matching names)
- Route Table Associations for each TGW attachment subnet
- Transit Gateway VPC attachment to existing shared Transit Gateway
- Optional routing configuration for TGW traffic

### Subnet Layout
| Subnet Name | CIDR Block | Availability Zone | Purpose |
|-------------|------------|-------------------|---------|
| **Transit Gateway Subnets** |
| TGW-ECOMM-DEV-1a | **********/26 | eu-west-1a | Transit Gateway Attachment |
| TGW-ECOMM-DEV-1b | 10.230.2.0/26 | eu-west-1b | Transit Gateway Attachment |
| **Private EKS Subnets** |
| private-eks-az1 | ***********/24 | eu-west-1a | EKS Worker Nodes |
| private-eks-az2 | ***********/24 | eu-west-1b | EKS Worker Nodes |
| **Private Load Balancer Subnets** |
| private-lb-az1 | 10.230.12.0/24 | eu-west-1a | Internal Load Balancers |
| private-lb-az2 | 10.230.13.0/24 | eu-west-1b | Internal Load Balancers |
| **Intra (Isolated) Subnets** |
| intra-az1 | 10.230.14.0/24 | eu-west-1a | Isolated Resources (No Internet) |
| intra-az2 | 10.230.15.0/24 | eu-west-1b | Isolated Resources (No Internet) |

## Prerequisites

1. **Terraform**: Version >= 1.0
2. **AWS CLI**: Configured with appropriate credentials
3. **AWS Provider**: Version ~> 5.0
4. **Bootstrap Project**: Must be applied first (see SETUP_GUIDE.md)

## Quick Start

### 1. Bootstrap Backend Infrastructure

```bash
# Create the S3 backend and DynamoDB table
cd bootstrap
terraform init
terraform apply
```

### 2. Deploy Main Infrastructure

```bash
# Return to main directory
cd ..

# Initialize with backend
terraform init

# Deploy with development configuration
terraform plan -var-file=dev.tfvars
terraform apply -var-file=dev.tfvars
```

## Transit Gateway Configuration

This project is designed to connect to an **existing Transit Gateway** that was created manually (not through Terraform). The configuration includes:

- **VPC Attachment**: Connects your VPC to the existing Transit Gateway
- **Dedicated Subnets**: Uses the 2 TGW attachment subnets for the connection
- **DNS Support**: Configurable DNS resolution through the Transit Gateway
- **Routing**: Optional routing configuration for TGW traffic

### Important Notes:
- You must provide the existing Transit Gateway ID in `terraform.tfvars`
- The Transit Gateway must be in the same AWS region as your VPC
- Ensure your AWS account has permissions to create VPC attachments to the Transit Gateway
- The attachment subnets should be in different Availability Zones for high availability

## Usage

### Environment Management

#### Development Environment
```bash
terraform plan -var-file=dev.tfvars
terraform apply -var-file=dev.tfvars
```

#### Production Environment
```bash
terraform plan -var-file=production.tfvars
terraform apply -var-file=production.tfvars
```

### Destroy Options

#### Option A: Destroy VPC Only (Recommended)
Destroys all VPC infrastructure while preserving the S3 backend:

**Windows (PowerShell):**
```powershell
.\destroy-vpc-only.ps1
```

**Linux/Mac (Bash):**
```bash
./destroy-vpc-only.sh
```

#### Option B: Complete Destroy (Including Backend)
⚠️ **WARNING**: This will destroy the S3 bucket containing your state file!

```bash
terraform destroy
```

**Note**: The S3 bucket has `prevent_destroy = true` protection in the bootstrap project, so you'll need to modify the bootstrap configuration first.

## Configuration

### Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `aws_region` | AWS region for resources | `eu-west-1` | No |
| `project_name` | Name of the project | `eks-main-test` | No |
| `environment` | Environment name | `dev` | No |
| `vpc_cidr` | CIDR block for VPC | `**********/16` | No |
| `tgw_subnet_cidrs` | CIDR blocks for TGW subnets | See variables.tf | No |
| `private_eks_subnet_cidrs` | CIDR blocks for EKS subnets | See variables.tf | No |
| `private_lb_subnet_cidrs` | CIDR blocks for LB subnets | See variables.tf | No |
| `intra_subnet_cidrs` | CIDR blocks for isolated subnets | See variables.tf | No |
| `map_public_ip_on_launch` | Auto-assign public IP | `true` | No |
| `existing_transit_gateway_id` | ID of existing Transit Gateway | `null` | Yes (if using TGW) |
| `create_transit_gateway_attachment` | Create TGW VPC attachment | `false` | No |
| `enable_dns_support_attachment` | Enable DNS support for TGW attachment | `true` | No |
| `enable_ipv6_support_attachment` | Enable IPv6 support for TGW attachment | `false` | No |
| `appliance_mode_support` | Enable appliance mode for TGW attachment | `disable` | No |

### Customization

To customize the subnet CIDRs, modify the `terraform.tfvars` file:

```hcl
# Example: Different subnet layout
tgw_subnet_cidrs = [
  "**********/26",
  "**********/26"
]

private_eks_subnet_cidrs = {
  az1 = "***********/24"
  az2 = "***********/24"
}
```

## Outputs

The following outputs are available after deployment:

- `vpc_id`: VPC ID
- `tgw_attachment_subnet_ids`: Transit Gateway subnet IDs
- `private_eks_subnet_ids`: Private EKS subnet IDs
- `private_lb_subnet_ids`: Private load balancer subnet IDs
- `intra_subnet_ids`: Intra (isolated) subnet IDs
- `all_subnet_ids`: All subnet IDs combined
- And more... (see outputs.tf)

## Next Steps

After deploying this VPC infrastructure, you can:

1. **Deploy EKS Cluster**: Use the private EKS subnets for EKS node groups
2. **Setup Transit Gateway**: Attach the TGW subnets to your Transit Gateway
3. **Configure Security Groups**: Customize security groups based on your requirements
4. **Add NAT Gateways**: If you need private subnets with internet access

## Security Considerations

- The private subnets are configured with no direct internet access
- Security group allows HTTP/HTTPS from anywhere and SSH from within VPC
- Modify security group rules based on your security requirements
- Intra subnets provide complete isolation for sensitive workloads

## Support

For issues or questions, please refer to:
- [Setup Guide](SETUP_GUIDE.md) - Complete setup instructions
- [Bootstrap Documentation](bootstrap/README.md) - Backend infrastructure details
- [Subnet Architecture](SUBNET_ARCHITECTURE.md) - Detailed subnet design
- [Transit Gateway Setup](TRANSIT_GATEWAY_SETUP.md) - TGW configuration guide
