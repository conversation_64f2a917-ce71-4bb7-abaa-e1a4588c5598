# Terraform EKS Infrastructure Setup Guide

This guide explains how to properly set up the Terraform EKS infrastructure using the new bootstrap approach to avoid circular dependencies.

## 🚨 Critical Architecture Change

The project has been restructured to follow best practices by separating the backend infrastructure (S3 bucket and DynamoDB table) from the main EKS infrastructure. This eliminates the dangerous circular dependency that existed in the previous version.

## 📁 Project Structure

```
Terraform-EKS-/
├── bootstrap/                    # Backend infrastructure (runs once)
│   ├── main.tf                  # S3 bucket and DynamoDB table
│   ├── variables.tf             # Bootstrap variables
│   ├── outputs.tf               # Backend configuration outputs
│   ├── terraform.tfvars         # Bootstrap configuration
│   └── README.md                # Bootstrap documentation
├── config/                      # Backend configuration files
│   ├── dev.backend.hcl          # Development backend config
│   └── prod.backend.hcl         # Production backend config
├── *.tf                         # Main EKS infrastructure
├── dev.tfvars                   # Development environment variables
├── production.tfvars            # Production environment variables
└── README.md                    # Main project documentation
```

## 🚀 Setup Process

### Step 1: Bootstrap the Backend Infrastructure

First, create the S3 bucket and DynamoDB table that will store Terraform state:

```bash
# Navigate to the bootstrap directory
cd bootstrap

# Configure the bootstrap (edit terraform.tfvars if needed)
# The default configuration creates:
# - S3 bucket: eks-main-terraform-state-ireland-unique
# - DynamoDB table: terraform-state-lock

# Initialize and apply the bootstrap
terraform init
terraform plan
terraform apply
```

**Important**: The bootstrap project uses local state to avoid circular dependencies.

### Step 2: Verify Bootstrap Outputs

After successful bootstrap deployment, note the outputs:

```bash
terraform output
```

The key outputs are:
- `terraform_state_bucket_id`: S3 bucket ID
- `dynamodb_table_name`: DynamoDB table name
- `backend_config_hcl`: Backend configuration in HCL format

### Step 3: Configure Main Project Backend

The main project is already configured to use the bootstrap-created backend. The configuration in `provider.tf` is:

```hcl
backend "s3" {
  bucket       = "eks-main-terraform-state-ireland-unique"
  key          = "terraform.tfstate"
  dynamodb_table = "terraform-state-lock"
  region       = "eu-west-1"
  encrypt      = true
  use_lockfile = true
}
```

### Step 4: Deploy Main Infrastructure

```bash
# Return to the main project directory
cd ..

# Initialize with the backend
terraform init

# Plan the deployment
terraform plan -var-file=dev.tfvars

# Apply the configuration
terraform apply -var-file=dev.tfvars
```

## 🔄 Environment Management

### Development Environment

```bash
# Use development configuration
terraform plan -var-file=dev.tfvars
terraform apply -var-file=dev.tfvars
```

### Production Environment

```bash
# Use production configuration
terraform plan -var-file=production.tfvars
terraform apply -var-file=production.tfvars
```

## 🛡️ Security Benefits

The new architecture provides several security improvements:

1. **No Circular Dependencies**: Backend infrastructure is managed separately
2. **State Protection**: S3 bucket has `prevent_destroy = true`
3. **Encryption**: Both S3 and DynamoDB use encryption at rest
4. **Access Control**: Public access is blocked on S3 bucket
5. **Versioning**: S3 bucket has versioning enabled for state history

## 🔧 Troubleshooting

### Bootstrap Issues

**Error: Bucket already exists**
- The S3 bucket name must be globally unique
- Update `bootstrap/terraform.tfvars` with a unique bucket name

**Error: Insufficient permissions**
- Ensure AWS credentials have permissions for S3 and DynamoDB
- Required permissions: `s3:CreateBucket`, `dynamodb:CreateTable`

### Main Project Issues

**Error: Backend not found**
- Ensure the bootstrap project was applied successfully
- Verify the bucket and DynamoDB table exist
- Check the backend configuration in `provider.tf`

**Error: State lock**
- Check if another Terraform operation is running
- Verify DynamoDB table exists and is accessible

## 🧹 Cleanup

### Cleanup Main Infrastructure

```bash
# Destroy VPC infrastructure only (preserves backend)
./destroy-vpc-only.sh

# Or destroy everything (including backend - DANGEROUS)
terraform destroy
```

### Cleanup Backend Infrastructure

```bash
# Navigate to bootstrap directory
cd bootstrap

# Destroy backend infrastructure
terraform destroy
```

**⚠️ WARNING**: Destroying the backend will affect ALL Terraform projects using it.

## 📋 Prerequisites

- AWS CLI configured with appropriate permissions
- Terraform >= 1.0
- Permissions to create S3 buckets and DynamoDB tables
- Existing Transit Gateway (if using TGW integration)

## 🔄 Migration from Old Version

If you have an existing deployment with the old architecture:

1. **Backup your state**: Copy the current `terraform.tfstate` file
2. **Apply bootstrap**: Run the bootstrap project first
3. **Migrate state**: Use `terraform init -migrate-state` to move to S3 backend
4. **Remove old backend**: Delete the `backend.tf` file (already done)
5. **Update variables**: Remove backend-related variables from `.tfvars` files

## 📚 Additional Resources

- [Bootstrap Project README](bootstrap/README.md)
- [Main Project README](README.md)
- [Subnet Architecture](SUBNET_ARCHITECTURE.md)
- [Transit Gateway Setup](TRANSIT_GATEWAY_SETUP.md) 