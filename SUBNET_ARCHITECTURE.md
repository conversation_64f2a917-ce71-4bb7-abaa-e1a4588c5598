# Subnet Architecture Overview

This document describes the comprehensive subnet architecture for the EKS VPC deployment.

## Architecture Summary

- **VPC CIDR**: 10.230.0.0/16
- **Total Subnets**: 8 subnets across 2 Availability Zones
- **Availability Zones**: eu-west-1a and eu-west-1c
- **Individual Route Tables**: Each subnet has its own dedicated route table

## Subnet Layout

### Transit Gateway Subnets
| Subnet Name | CIDR Block | Availability Zone | Route Table | Purpose |
|-------------|------------|-------------------|-------------|---------|
| TGW-ECOMM-DEV-1a | 10.230.1.0/24 | eu-west-1a | TGW-ECOMM-DEV-1a | Transit Gateway Attachment |
| TGW-ECOMM-DEV-1b | 10.230.2.0/24 | eu-west-1c | TGW-ECOMM-DEV-1b | Transit Gateway Attachment |

### Private EKS Subnets
| Subnet Name | CIDR Block | Availability Zone | Route Table | Purpose |
|-------------|------------|-------------------|-------------|---------|
| private-eks-az1 | 10.230.10.0/24 | eu-west-1a | private-eks-az1-rt | EKS Worker Nodes |
| private-eks-az2 | 10.230.11.0/24 | eu-west-1c | private-eks-az2-rt | EKS Worker Nodes |

### Private Load Balancer Subnets
| Subnet Name | CIDR Block | Availability Zone | Route Table | Purpose |
|-------------|------------|-------------------|-------------|---------|
| private-lb-az1 | 10.230.12.0/24 | eu-west-1a | private-lb-az1-rt | Internal Load Balancers |
| private-lb-az2 | 10.230.13.0/24 | eu-west-1c | private-lb-az2-rt | Internal Load Balancers |

### Intra (Isolated) Subnets
| Subnet Name | CIDR Block | Availability Zone | Route Table | Purpose |
|-------------|------------|-------------------|-------------|---------|
| intra-az1 | 10.230.14.0/24 | eu-west-1a | intra-az1-rt | Isolated Resources (No Internet) |
| intra-az2 | 10.230.15.0/24 | eu-west-1c | intra-az2-rt | Isolated Resources (No Internet) |

## Subnet Characteristics

### Transit Gateway Subnets
- **Internet Access**: No direct internet access
- **Route Table**: Individual route tables with TGW routes
- **Purpose**: Dedicated for Transit Gateway VPC attachment
- **Naming**: Follows TGW-ECOMM-DEV-{zone} pattern

### Private EKS Subnets
- **Internet Access**: No direct internet access (private)
- **Route Table**: Individual route tables for each AZ
- **Purpose**: EKS worker nodes and pods
- **Tags**: Include `kubernetes.io/role/internal-elb=1` for EKS integration
- **Naming**: private-eks-az{1|2}

### Private Load Balancer Subnets
- **Internet Access**: No direct internet access (private)
- **Route Table**: Individual route tables for each AZ
- **Purpose**: Internal Application Load Balancers and Network Load Balancers
- **Tags**: Include `kubernetes.io/role/internal-elb=1` for EKS integration
- **Naming**: private-lb-az{1|2}

### Intra (Isolated) Subnets
- **Internet Access**: Completely isolated (no internet access)
- **Route Table**: Individual route tables with no default routes
- **Purpose**: Highly secure resources that need complete isolation
- **Naming**: intra-az{1|2}

## Availability Zone Mapping

### eu-west-1a (AZ1)
- TGW-ECOMM-DEV-1a (10.230.1.0/24)
- private-eks-az1 (10.230.10.0/24)
- private-lb-az1 (10.230.12.0/24)
- intra-az1 (10.230.14.0/24)

### eu-west-1c (AZ2)
- TGW-ECOMM-DEV-1b (10.230.2.0/24)
- private-eks-az2 (10.230.11.0/24)
- private-lb-az2 (10.230.13.0/24)
- intra-az2 (10.230.15.0/24)

## Route Table Strategy

Each subnet has its own dedicated route table for maximum flexibility:

1. **TGW Route Tables**: Can route traffic through Transit Gateway
2. **Private EKS Route Tables**: Can be configured with NAT Gateway routes for internet access
3. **Private LB Route Tables**: Optimized for load balancer traffic patterns
4. **Intra Route Tables**: No default routes for complete isolation

## EKS Integration

### EKS-Specific Tags
Private EKS and Load Balancer subnets include the tag:
```
"kubernetes.io/role/internal-elb" = "1"
```

This tag enables:
- Automatic subnet discovery by EKS
- Internal load balancer placement
- Proper integration with AWS Load Balancer Controller

### Recommended Usage
- **EKS Cluster**: Deploy in private-eks-az1 and private-eks-az2
- **Internal ALB/NLB**: Deploy in private-lb-az1 and private-lb-az2
- **Database/Cache**: Deploy in intra-az1 and intra-az2 for maximum security

## Security Considerations

1. **Network Isolation**: Each subnet type serves a specific purpose
2. **Route Table Separation**: Individual route tables prevent unintended routing
3. **No Public IPs**: All subnets have `map_public_ip_on_launch = false`
4. **Intra Subnets**: Completely isolated for sensitive workloads

## Future Extensibility

The architecture supports easy addition of:
- NAT Gateways for private subnet internet access
- VPC Endpoints for AWS service access
- Additional subnets in other Availability Zones
- Custom routing rules per subnet type
