# Command to destroy VPC infrastructure while preserving S3 backend
# Copy and paste this command into PowerShell:

terraform destroy -target="aws_vpc.main" -target="aws_subnet.private_eks_az1" -target="aws_subnet.private_eks_az2" -target="aws_subnet.private_lb_az1" -target="aws_subnet.private_lb_az2" -target="aws_subnet.intra_az1" -target="aws_subnet.intra_az2" -target="aws_subnet.tgw_attachment_1a" -target="aws_subnet.tgw_attachment_1b" -target="aws_ec2_transit_gateway_vpc_attachment.main" -target="aws_internet_gateway.main" -target="aws_security_group.general_use" -target="aws_route_table.private_eks_az1" -target="aws_route_table.private_eks_az2" -target="aws_route_table.private_lb_az1" -target="aws_route_table.private_lb_az2" -target="aws_route_table.intra_az1" -target="aws_route_table.intra_az2" -target="aws_route_table.tgw_attachment_1a" -target="aws_route_table.tgw_attachment_1b"

# This command will:
# - Destroy all VPC infrastructure (subnets, route tables, IGW, security groups, TGW attachment)
# - PRESERVE the S3 bucket with your state file
# - PRESERVE the DynamoDB table for state locking
# - Allow you to redeploy anytime with: terraform apply
