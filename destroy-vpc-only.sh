#!/bin/bash
# <PERSON>sh script to destroy VPC infrastructure while preserving S3 backend
# This script targets only the VPC resources and excludes the S3 backend infrastructure

echo "🚀 Starting VPC-only destruction (preserving S3 backend)..."

# Define the resources to destroy (VPC infrastructure only)
VPC_RESOURCES=(
    "aws_vpc.main"
    "aws_subnet.private_eks_az1"
    "aws_subnet.private_eks_az2" 
    "aws_subnet.private_lb_az1"
    "aws_subnet.private_lb_az2"
    "aws_subnet.intra_az1"
    "aws_subnet.intra_az2"
    "aws_subnet.tgw_attachment_1a"
    "aws_subnet.tgw_attachment_1b"
    "aws_ec2_transit_gateway_vpc_attachment.main"
    "aws_internet_gateway.main"
    "aws_security_group.general_use"
    "aws_route_table.private_eks_az1"
    "aws_route_table.private_eks_az2"
    "aws_route_table.private_lb_az1" 
    "aws_route_table.private_lb_az2"
    "aws_route_table.intra_az1"
    "aws_route_table.intra_az2"
    "aws_route_table.tgw_attachment_1a"
    "aws_route_table.tgw_attachment_1b"
)

# Build the terraform destroy command with all target resources
TARGET_ARGS=""
for resource in "${VPC_RESOURCES[@]}"; do
    TARGET_ARGS="$TARGET_ARGS -target=\"$resource\""
done

echo "📋 Resources to be destroyed:"
for resource in "${VPC_RESOURCES[@]}"; do
    echo "  - $resource"
done

echo ""
echo "🔒 Resources that will be PRESERVED:"
echo "  - aws_s3_bucket.terraform_state"
echo "  - aws_s3_bucket_versioning.terraform_state_versioning"
echo "  - aws_s3_bucket_server_side_encryption_configuration.default"
echo "  - aws_s3_bucket_public_access_block.terraform_state"
echo "  - aws_dynamodb_table.terraform_state_lock"

echo ""
echo "⚠️  WARNING: This will destroy all VPC infrastructure!"
read -p "Do you want to continue? (yes/no): " confirmation

if [ "$confirmation" = "yes" ]; then
    echo ""
    echo "🔥 Executing targeted destroy..."
    eval "terraform destroy $TARGET_ARGS"
    
    if [ $? -eq 0 ]; then
        echo ""
        echo "✅ VPC infrastructure destroyed successfully!"
        echo "💾 S3 backend infrastructure preserved."
        echo "🚀 You can redeploy anytime with: terraform apply"
    else
        echo ""
        echo "❌ Destroy operation failed!"
    fi
else
    echo ""
    echo "🛑 Operation cancelled."
fi
