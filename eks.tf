# EKS Cluster Configuration using terraform-aws-modules/eks/aws
module "eks" {
  source  = "terraform-aws-modules/eks/aws"
  version = "~> 20.0"

  # Cluster Configuration
  cluster_name    = "${var.project_name}-${var.environment}"
  cluster_version = var.eks_cluster_version

  # Cluster Addons
  bootstrap_self_managed_addons = false
  cluster_addons = {
    coredns = {
      configuration_values = jsonencode({
        computeType = "Fargate"
        # Ensure CoreDNS runs on Fargate
      })
    }
    eks-pod-identity-agent = {}
    kube-proxy             = {}
    vpc-cni = {
      configuration_values = jsonencode({
        env = {
          # Enable prefix delegation for better IP utilization
          ENABLE_PREFIX_DELEGATION = "true"
          WARM_PREFIX_TARGET       = "1"
        }
      })
    }
  }

  # Cluster Access Configuration
  cluster_endpoint_public_access  = var.cluster_endpoint_public_access
  cluster_endpoint_private_access = true

  # Public access restrictions (if public access is enabled)
  cluster_endpoint_public_access_cidrs = var.cluster_endpoint_public_access_cidrs

  # Enable cluster creator admin permissions
  enable_cluster_creator_admin_permissions = true

  # Network Configuration - Using your existing VPC and subnets
  vpc_id     = aws_vpc.main.id
  subnet_ids = [
    aws_subnet.private_eks_az1.id,
    aws_subnet.private_eks_az2.id
  ]

  # Control plane subnets (using private LB subnets for control plane)
  control_plane_subnet_ids = [
    aws_subnet.private_lb_az1.id,
    aws_subnet.private_lb_az2.id
  ]

  # EKS Managed Node Group Defaults
  eks_managed_node_group_defaults = {
    instance_types = var.eks_node_group_instance_types

    # Use custom launch template
    use_custom_launch_template = false

    # Disk configuration
    disk_size = var.eks_node_group_disk_size

    # AMI configuration
    ami_type = var.eks_node_group_ami_type

    # Networking
    subnet_ids = [
      aws_subnet.private_eks_az1.id,
      aws_subnet.private_eks_az2.id
    ]

    # Security groups
    vpc_security_group_ids = [aws_security_group.general_use.id]

    # Node group update configuration
    update_config = {
      max_unavailable_percentage = 25
    }

    # Taints and labels
    taints = {}
    labels = {
      Environment = var.environment
      Project     = var.project_name
    }
  }

  # EKS Managed Node Groups
  eks_managed_node_groups = {
    # Primary node group for general workloads
    primary = {
      name           = "${var.project_name}-${var.environment}-primary"
      ami_type       = var.eks_node_group_ami_type
      instance_types = var.eks_node_group_instance_types
      capacity_type  = var.eks_node_group_capacity_type

      min_size     = var.eks_node_group_min_size
      max_size     = var.eks_node_group_max_size
      desired_size = var.eks_node_group_desired_size

      # Node group specific configuration
      labels = {
        Environment = var.environment
        Project     = var.project_name
        NodeGroup   = "primary"
      }

      taints = []

      # Launch template configuration
      create_launch_template = true
      launch_template_name   = "${var.project_name}-${var.environment}-primary"

      # User data for additional node configuration
      pre_bootstrap_user_data = <<-EOT
        #!/bin/bash
        # Additional node setup can go here
        echo "Setting up EKS node for ${var.project_name}-${var.environment}"
      EOT

      # Block device mappings
      block_device_mappings = {
        xvda = {
          device_name = "/dev/xvda"
          ebs = {
            volume_size           = var.eks_node_group_disk_size
            volume_type           = "gp3"
            iops                  = 3000
            throughput            = 150
            encrypted             = true
            delete_on_termination = true
          }
        }
      }
    }
  }

  # Fargate Profiles (optional)
  fargate_profiles = var.enable_fargate ? {
    default = {
      name = "${var.project_name}-${var.environment}-fargate"
      selectors = [
        {
          namespace = "kube-system"
          labels = {
            k8s-app = "kube-dns"
          }
        },
        {
          namespace = "default"
        }
      ]

      # Use private subnets for Fargate
      subnet_ids = [
        aws_subnet.private_eks_az1.id,
        aws_subnet.private_eks_az2.id
      ]

      tags = {
        Environment = var.environment
        Project     = var.project_name
        Type        = "Fargate"
      }
    }
  } : {}

  # Cluster Security Group
  cluster_security_group_additional_rules = {
    ingress_nodes_ephemeral_ports_tcp = {
      description                = "Nodes on ephemeral ports"
      protocol                   = "tcp"
      from_port                  = 1025
      to_port                    = 65535
      type                       = "ingress"
      source_node_security_group = true
    }
  }

  # Node Security Group
  node_security_group_additional_rules = {
    ingress_self_all = {
      description = "Node to node all ports/protocols"
      protocol    = "-1"
      from_port   = 0
      to_port     = 0
      type        = "ingress"
      self        = true
    }

    # Allow nodes to communicate with Transit Gateway
    egress_all = {
      description      = "Node all egress"
      protocol         = "-1"
      from_port        = 0
      to_port          = 0
      type             = "egress"
      cidr_blocks      = ["0.0.0.0/0"]
      ipv6_cidr_blocks = ["::/0"]
    }
  }

  # Tags
  tags = merge(
    var.common_tags,
    {
      Environment = var.environment
      Project     = var.project_name
      Terraform   = "true"
      EKSCluster  = "${var.project_name}-${var.environment}"
    }
  )
}

