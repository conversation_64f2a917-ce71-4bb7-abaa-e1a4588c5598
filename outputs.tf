# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}


# Transit Gateway Attachment Subnet Outputs
output "tgw_attachment_subnet_ids" {
  description = "IDs of the Transit Gateway attachment subnets"
  value       = [
    aws_subnet.tgw_attachment_1a.id,
    aws_subnet.tgw_attachment_1b.id
  ]
}

output "tgw_attachment_subnet_cidrs" {
  description = "CIDR blocks of the Transit Gateway attachment subnets"
  value       = [
    aws_subnet.tgw_attachment_1a.cidr_block,
    aws_subnet.tgw_attachment_1b.cidr_block
  ]
}

output "tgw_attachment_subnet_azs" {
  description = "Availability zones of the Transit Gateway attachment subnets"
  value       = [
    aws_subnet.tgw_attachment_1a.availability_zone,
    aws_subnet.tgw_attachment_1b.availability_zone
  ]
}

# Individual TGW Subnet Outputs
output "tgw_subnet_1a_id" {
  description = "ID of TGW-ECOMM-DEV-1a subnet"
  value       = aws_subnet.tgw_attachment_1a.id
}

output "tgw_subnet_1b_id" {
  description = "ID of TGW-ECOMM-DEV-1b subnet"
  value       = aws_subnet.tgw_attachment_1b.id
}

output "tgw_route_table_1a_id" {
  description = "ID of TGW-ECOMM-DEV-1a route table"
  value       = aws_route_table.tgw_attachment_1a.id
}

output "tgw_route_table_1b_id" {
  description = "ID of TGW-ECOMM-DEV-1b route table"
  value       = aws_route_table.tgw_attachment_1b.id
}

# Private EKS Subnet Outputs
output "private_eks_subnet_ids" {
  description = "IDs of the private EKS subnets"
  value = {
    az1 = aws_subnet.private_eks_az1.id
    az2 = aws_subnet.private_eks_az2.id
  }
}

output "private_eks_subnet_cidrs" {
  description = "CIDR blocks of the private EKS subnets"
  value = {
    az1 = aws_subnet.private_eks_az1.cidr_block
    az2 = aws_subnet.private_eks_az2.cidr_block
  }
}

# Private Load Balancer Subnet Outputs
output "private_lb_subnet_ids" {
  description = "IDs of the private load balancer subnets"
  value = {
    az1 = aws_subnet.private_lb_az1.id
    az2 = aws_subnet.private_lb_az2.id
  }
}

output "private_lb_subnet_cidrs" {
  description = "CIDR blocks of the private load balancer subnets"
  value = {
    az1 = aws_subnet.private_lb_az1.cidr_block
    az2 = aws_subnet.private_lb_az2.cidr_block
  }
}

# Intra (Isolated) Subnet Outputs
output "intra_subnet_ids" {
  description = "IDs of the intra (isolated) subnets"
  value = {
    az1 = aws_subnet.intra_az1.id
    az2 = aws_subnet.intra_az2.id
  }
}

output "intra_subnet_cidrs" {
  description = "CIDR blocks of the intra (isolated) subnets"
  value = {
    az1 = aws_subnet.intra_az1.cidr_block
    az2 = aws_subnet.intra_az2.cidr_block
  }
}

# Route Table Outputs
output "private_eks_route_table_ids" {
  description = "IDs of the private EKS route tables"
  value = {
    az1 = aws_route_table.private_eks_az1.id
    az2 = aws_route_table.private_eks_az2.id
  }
}

output "private_lb_route_table_ids" {
  description = "IDs of the private load balancer route tables"
  value = {
    az1 = aws_route_table.private_lb_az1.id
    az2 = aws_route_table.private_lb_az2.id
  }
}

output "intra_route_table_ids" {
  description = "IDs of the intra (isolated) route tables"
  value = {
    az1 = aws_route_table.intra_az1.id
    az2 = aws_route_table.intra_az2.id
  }
}

output "tgw_attachment_route_table_ids" {
  description = "IDs of the TGW attachment route tables"
  value       = [
    aws_route_table.tgw_attachment_1a.id,
    aws_route_table.tgw_attachment_1b.id
  ]
}

# Security Group Output
output "general_use_security_group_id" {
  description = "ID of the general use security group"
  value       = aws_security_group.general_use.id
}

# All Subnet IDs (combined)
output "all_subnet_ids" {
  description = "All subnet IDs (TGW + Private EKS + Private LB + Intra)"
  value       = [
    aws_subnet.tgw_attachment_1a.id,
    aws_subnet.tgw_attachment_1b.id,
    aws_subnet.private_eks_az1.id,
    aws_subnet.private_eks_az2.id,
    aws_subnet.private_lb_az1.id,
    aws_subnet.private_lb_az2.id,
    aws_subnet.intra_az1.id,
    aws_subnet.intra_az2.id
  ]
}

# Availability Zones
output "availability_zones" {
  description = "List of availability zones used"
  value       = data.aws_availability_zones.available.names
}

# Transit Gateway Outputs (for existing TGW)
output "existing_transit_gateway_id" {
  description = "ID of the existing Transit Gateway being used"
  value       = var.existing_transit_gateway_id
}

output "transit_gateway_vpc_attachment_id" {
  description = "ID of the Transit Gateway VPC attachment"
  value       = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? aws_ec2_transit_gateway_vpc_attachment.main[0].id : null
}

# =============================================================================
# EKS Cluster Outputs
# =============================================================================

output "eks_cluster_id" {
  description = "The ID of the EKS cluster"
  value       = module.eks.cluster_id
}

output "eks_cluster_arn" {
  description = "The Amazon Resource Name (ARN) of the cluster"
  value       = module.eks.cluster_arn
}

output "eks_cluster_endpoint" {
  description = "Endpoint for your Kubernetes API server"
  value       = module.eks.cluster_endpoint
}

output "eks_cluster_version" {
  description = "The Kubernetes version for the cluster"
  value       = module.eks.cluster_version
}

output "eks_cluster_platform_version" {
  description = "Platform version for the cluster"
  value       = module.eks.cluster_platform_version
}

output "eks_cluster_status" {
  description = "Status of the EKS cluster. One of `CREATING`, `ACTIVE`, `DELETING`, `FAILED`"
  value       = module.eks.cluster_status
}

output "eks_cluster_security_group_id" {
  description = "Cluster security group that was created by Amazon EKS for the cluster"
  value       = module.eks.cluster_security_group_id
}

output "eks_cluster_iam_role_name" {
  description = "IAM role name associated with EKS cluster"
  value       = module.eks.cluster_iam_role_name
}

output "eks_cluster_iam_role_arn" {
  description = "IAM role ARN associated with EKS cluster"
  value       = module.eks.cluster_iam_role_arn
}

output "eks_cluster_certificate_authority_data" {
  description = "Base64 encoded certificate data required to communicate with the cluster"
  value       = module.eks.cluster_certificate_authority_data
}

output "eks_cluster_primary_security_group_id" {
  description = "Cluster security group that was created by Amazon EKS for the cluster. Managed node groups use this security group for control-plane-to-data-plane communication. Referred to as 'Cluster security group' in the EKS console"
  value       = module.eks.cluster_primary_security_group_id
}

output "eks_oidc_provider" {
  description = "The OpenID Connect identity provider (issuer URL without leading `https://`)"
  value       = module.eks.oidc_provider
}

output "eks_oidc_provider_arn" {
  description = "The ARN of the OIDC Provider if `enable_irsa = true`"
  value       = module.eks.oidc_provider_arn
}

# Node Groups
output "eks_managed_node_groups" {
  description = "Map of attribute maps for all EKS managed node groups created"
  value       = module.eks.eks_managed_node_groups
}

output "eks_managed_node_groups_autoscaling_group_names" {
  description = "List of the autoscaling group names created by EKS managed node groups"
  value       = module.eks.eks_managed_node_groups_autoscaling_group_names
}

# Fargate
output "fargate_profiles" {
  description = "Map of attribute maps for all EKS Fargate profiles created"
  value       = module.eks.fargate_profiles
}

# Additional useful outputs
output "eks_cluster_name" {
  description = "The name of the EKS cluster"
  value       = module.eks.cluster_name
}

output "kubectl_config_command" {
  description = "kubectl config command to configure local access to the cluster"
  value       = "aws eks --region ${var.aws_region} update-kubeconfig --name ${module.eks.cluster_name}"
}

output "transit_gateway_vpc_attachment_state" {
  description = "State of the Transit Gateway VPC attachment"
  value       = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? "available" : null
}

output "transit_gateway_vpc_attachment_vpc_owner_id" {
  description = "ID of the AWS account that owns the VPC"
  value       = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? aws_ec2_transit_gateway_vpc_attachment.main[0].vpc_owner_id : null
}


