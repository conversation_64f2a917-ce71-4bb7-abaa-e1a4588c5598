# Route Entries for All Subnets
# This file contains all route entries that direct traffic through various gateways

# =============================================================================
# Transit Gateway Route Entries for TGW Attachment Subnets
# =============================================================================

resource "aws_route" "tgw_attachment_1a_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.tgw_attachment_1a.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

resource "aws_route" "tgw_attachment_1b_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.tgw_attachment_1b.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

# =============================================================================
# Transit Gateway Route Entries for Private EKS Subnets
# =============================================================================

resource "aws_route" "private_eks_az1_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_eks_az1.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

resource "aws_route" "private_eks_az2_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_eks_az2.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

# =============================================================================
# Transit Gateway Route Entries for Private Load Balancer Subnets
# =============================================================================

resource "aws_route" "private_lb_az1_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_lb_az1.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

resource "aws_route" "private_lb_az2_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_lb_az2.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

# =============================================================================
# Transit Gateway Route Entries for Intra (Isolated) Subnets
# =============================================================================

#resource "aws_route" "intra_az1_to_tgw" {
#  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

#  route_table_id         = aws_route_table.intra_az1.id
##  destination_cidr_block = "0.0.0.0/0"
#  transit_gateway_id     = var.existing_transit_gateway_id

#  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
#}

#resource "aws_route" "intra_az2_to_tgw" {
#  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

#  route_table_id         = aws_route_table.intra_az2.id
#  destination_cidr_block = "0.0.0.0/0"
#  transit_gateway_id     = var.existing_transit_gateway_id

#  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
#}


