# Route Tables for Private EKS Subnets
resource "aws_route_table" "private_eks_az1" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-eks-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private EKS Route Table"
      AZ          = var.availability_zones[0] # "eu-west-1a"
    }
  )
}

resource "aws_route_table" "private_eks_az2" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-eks-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private EKS Route Table"
      AZ          = var.availability_zones[1] # "eu-west-1b"
    }
  )
}

# Route Tables for Private Load Balancer Subnets
resource "aws_route_table" "private_lb_az1" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-lb-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private LB Route Table"
      AZ          = var.availability_zones[0] # "eu-west-1a"
    }
  )
}

resource "aws_route_table" "private_lb_az2" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-lb-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private LB Route Table"
      AZ          = var.availability_zones[1] # "eu-west-1b"
    }
  )
}

# Route Tables for Intra (Isolated) Subnets
resource "aws_route_table" "intra_az1" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "intra-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated) Route Table"
      AZ          = var.availability_zones[0] # "eu-west-1a"
    }
  )
}

resource "aws_route_table" "intra_az2" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "intra-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated) Route Table"
      AZ          = var.availability_zones[1] # "eu-west-1b"
    }
  )
}

# Route Table Associations for Private EKS Subnets
resource "aws_route_table_association" "private_eks_az1" {
  subnet_id      = aws_subnet.private_eks_az1.id
  route_table_id = aws_route_table.private_eks_az1.id
}

resource "aws_route_table_association" "private_eks_az2" {
  subnet_id      = aws_subnet.private_eks_az2.id
  route_table_id = aws_route_table.private_eks_az2.id
}

# Route Table Associations for Private Load Balancer Subnets
resource "aws_route_table_association" "private_lb_az1" {
  subnet_id      = aws_subnet.private_lb_az1.id
  route_table_id = aws_route_table.private_lb_az1.id
}

resource "aws_route_table_association" "private_lb_az2" {
  subnet_id      = aws_subnet.private_lb_az2.id
  route_table_id = aws_route_table.private_lb_az2.id
}

# Route Table Associations for Intra (Isolated) Subnets
resource "aws_route_table_association" "intra_az1" {
  subnet_id      = aws_subnet.intra_az1.id
  route_table_id = aws_route_table.intra_az1.id
}

resource "aws_route_table_association" "intra_az2" {
  subnet_id      = aws_subnet.intra_az2.id
  route_table_id = aws_route_table.intra_az2.id
}

# Route Tables for TGW Attachment Subnets
resource "aws_route_table" "tgw_attachment_1a" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "TGW-${var.project_name}-1a"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Transit Gateway Route Table"
      AZ          = var.availability_zones[0] # "eu-west-1a"
    }
  )
}

resource "aws_route_table" "tgw_attachment_1b" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "TGW-${var.project_name}-1b"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Transit Gateway Route Table"
      AZ          = var.availability_zones[1] # "eu-west-1b"
    }
  )
}

# Route Table Associations for TGW Attachment Subnets
resource "aws_route_table_association" "tgw_attachment_1a" {
  subnet_id      = aws_subnet.tgw_attachment_1a.id
  route_table_id = aws_route_table.tgw_attachment_1a.id
}

resource "aws_route_table_association" "tgw_attachment_1b" {
  subnet_id      = aws_subnet.tgw_attachment_1b.id
  route_table_id = aws_route_table.tgw_attachment_1b.id
}

# Optional: Add routes to Transit Gateway in TGW route table 1a
resource "aws_route" "tgw_to_general_subnets_1a" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.tgw_attachment_1a.id
  destination_cidr_block = "0.0.0.0/0"  # Or specify specific CIDR blocks you want to route through TGW
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

# Optional: Add routes to Transit Gateway in TGW route table 1b
resource "aws_route" "tgw_to_general_subnets_1b" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.tgw_attachment_1b.id
  destination_cidr_block = "0.0.0.0/0"  # Or specify specific CIDR blocks you want to route through TGW
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
} 