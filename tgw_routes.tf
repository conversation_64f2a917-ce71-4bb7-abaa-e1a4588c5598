# Transit Gateway Route Entries
# This file contains all route entries that direct traffic to the Transit Gateway

# Routes for TGW Attachment Subnets (internal TGW subnet routing)
resource "aws_route" "tgw_to_general_subnets_1a" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.tgw_attachment_1a.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

resource "aws_route" "tgw_to_general_subnets_1b" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.tgw_attachment_1b.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

# Routes for Private EKS Subnets to Transit Gateway
resource "aws_route" "private_eks_az1_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_eks_az1.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

resource "aws_route" "private_eks_az2_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_eks_az2.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

# Routes for Private Load Balancer Subnets to Transit Gateway
resource "aws_route" "private_lb_az1_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_lb_az1.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

resource "aws_route" "private_lb_az2_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.private_lb_az2.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

# Routes for Intra (Isolated) Subnets to Transit Gateway
resource "aws_route" "intra_az1_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.intra_az1.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}

resource "aws_route" "intra_az2_to_tgw" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.intra_az2.id
  destination_cidr_block = "0.0.0.0/0"
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}
