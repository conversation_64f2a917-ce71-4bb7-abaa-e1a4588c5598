# AWS Region
variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "eu-west-1"
}

# Project Name
variable "project_name" {
  description = "EKS-deployment-test"
  type        = string
  default     = "eks-main-test"
}

# Environment
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

# VPC CIDR Block
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "**********/16"
}

# VPC Availability Zones
variable "availability_zones" {
  description = "List of availability zones"
  type        = list(string)
  default     = ["eu-west-1a", "eu-west-1b"]
}

# Transit Gateway Attachment Subnet CIDRs
variable "tgw_subnet_cidrs" {
  description = "CIDR blocks for Transit Gateway attachment subnets"
  type        = list(string)
  default = [
    "**********/26",  # TGW Subnet 1
    "**********/26"   # TGW Subnet 2
  ]
}

# Private EKS Subnet CIDRs
variable "private_eks_subnet_cidrs" {
  description = "CIDR blocks for private EKS subnets"
  type        = map(string)
  default = {
    az1 = "***********/24"  
    az2 = "***********/24"  
  }
}

# Private Load Balancer Subnet CIDRs
variable "private_lb_subnet_cidrs" {
  description = "CIDR blocks for private load balancer subnets"
  type        = map(string)
  default = {
    az1 = "***********/24" 
    az2 = "***********/24" 
  }
}

# Intra (Isolated) Subnet CIDRs
variable "intra_subnet_cidrs" {
  description = "CIDR blocks for intra (isolated) subnets"
  type        = map(string)
  default = {
    az1 = "***********/24" 
    az2 = "***********/24"  
  }
}

# Map Public IP on Launch
variable "map_public_ip_on_launch" {
  description = "Should be false if you do not want to auto-assign public IP on launch"
  type        = bool
  default     = true
}

# Additional tags
variable "additional_tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# IAM User Access Key ID (for local testing)
variable "iam_user_access_key_id" {
  description = "IAM user access key ID for local testing"
  type        = string
  default     = null
  sensitive   = true
}

# IAM User Secret Access Key (for local testing)
variable "iam_user_secret_access_key" {
  description = "IAM user secret access key for local testing"
  type        = string
  default     = null
  sensitive   = true
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
  }
}

# Transit Gateway Configuration
variable "existing_transit_gateway_id" {
  description = "ID of existing Transit Gateway to attach VPC to (if not creating new one)"
  type        = string
  default     = null
}

variable "create_transit_gateway_attachment" {
  description = "Whether to create a Transit Gateway VPC attachment"
  type        = bool
  default     = false
}

variable "enable_dns_support_attachment" {
  description = "Whether DNS support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = true
}

variable "enable_ipv6_support_attachment" {
  description = "Whether IPv6 support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = false
}

variable "appliance_mode_support" {
  description = "Whether Appliance Mode support is enabled for the Transit Gateway VPC attachment"
  type        = string
  default     = "disable"
  validation {
    condition     = contains(["enable", "disable"], var.appliance_mode_support)
    error_message = "Appliance mode support must be either 'enable' or 'disable'."
  }
}

# =============================================================================
# EKS Configuration Variables
# =============================================================================

variable "eks_cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
  default     = "1.31"
}

variable "cluster_endpoint_public_access" {
  description = "Whether the Amazon EKS public API server endpoint is enabled"
  type        = bool
  default     = false
}

variable "cluster_endpoint_public_access_cidrs" {
  description = "List of CIDR blocks that can access the Amazon EKS public API server endpoint"
  type        = list(string)
  default     = ["0.0.0.0/0"]
}

variable "eks_node_group_instance_types" {
  description = "List of instance types for EKS managed node groups"
  type        = list(string)
  default     = ["m6i.large", "m5.large", "m5n.large", "m5zn.large"]
}

variable "eks_node_group_ami_type" {
  description = "Type of Amazon Machine Image (AMI) associated with the EKS Node Group"
  type        = string
  default     = "AL2023_x86_64_STANDARD"
  validation {
    condition = contains([
      "AL2_x86_64", "AL2_x86_64_GPU", "AL2_ARM_64",
      "AL2023_x86_64_STANDARD", "AL2023_ARM_64_STANDARD",
      "CUSTOM", "BOTTLEROCKET_ARM_64", "BOTTLEROCKET_x86_64"
    ], var.eks_node_group_ami_type)
    error_message = "AMI type must be a valid EKS AMI type."
  }
}

variable "eks_node_group_capacity_type" {
  description = "Type of capacity associated with the EKS Node Group. Valid values: ON_DEMAND, SPOT"
  type        = string
  default     = "ON_DEMAND"
  validation {
    condition     = contains(["ON_DEMAND", "SPOT"], var.eks_node_group_capacity_type)
    error_message = "Capacity type must be either 'ON_DEMAND' or 'SPOT'."
  }
}

variable "eks_node_group_disk_size" {
  description = "Disk size in GiB for worker nodes"
  type        = number
  default     = 50
}

variable "eks_node_group_min_size" {
  description = "Minimum number of nodes in the EKS managed node group"
  type        = number
  default     = 1
}

variable "eks_node_group_max_size" {
  description = "Maximum number of nodes in the EKS managed node group"
  type        = number
  default     = 10
}

variable "eks_node_group_desired_size" {
  description = "Desired number of nodes in the EKS managed node group"
  type        = number
  default     = 2
}

variable "enable_fargate" {
  description = "Whether to enable Fargate profiles for the EKS cluster"
  type        = bool
  default     = false
}
